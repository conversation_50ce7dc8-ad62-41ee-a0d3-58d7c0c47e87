# Getting Started with <PERSON><PERSON>: A Guide for Flutter Developers

If you have experience with Flutter, you already understand many of the core concepts that make <PERSON><PERSON> powerful. This guide will help you leverage your Flutter knowledge to quickly get productive with <PERSON><PERSON>.

## What is Valdi?

Valdi is a cross-platform UI framework that lets you build native mobile applications using TypeScript/JavaScript. Like Flutter, it uses a component-based architecture and reactive programming patterns, but renders directly to native platform views instead of using a custom graphics engine.

## How Your Flutter Knowledge Applies

Your Flutter experience gives you a solid foundation for Valdi development:

### Familiar Concepts
- **Component-based architecture**: Like Flutter widgets, <PERSON><PERSON> uses reusable components
- **Reactive programming**: UI updates automatically when state changes
- **Declarative syntax**: Describe your UI declaratively using TSX/JSX
- **Hot reload**: Fast development iteration with state preservation
- **Cross-platform development**: Write once, run on iOS and Android
- **State management**: Similar patterns for managing component state
- **Lifecycle methods**: Component initialization, rendering, and cleanup

### Key Differences to Understand
- **Language**: TypeScript/JavaScript instead of Dart
- **Rendering**: Native platform views instead of custom graphics engine
- **Layout**: CSS Flexbox instead of <PERSON>lut<PERSON>'s layout system
- **Styling**: CSS-like properties instead of <PERSON>lut<PERSON>'s styling approach
- **Side-effect rendering**: JSX emitted as statements instead of returned values

## Core Concept Mappings

Understanding how Valdi concepts relate to Flutter will help you leverage your existing knowledge:

| Flutter Concept | Valdi Equivalent | What You Need to Know |
|----------------|------------------|----------------------|
| **Widget** | Component | Basic building block for UI, but renders to native views |
| **StatelessWidget** | Component | UI component without internal state |
| **StatefulWidget** | StatefulComponent | UI component with internal state |
| **build() method** | onRender() method | Describes the UI, but emits JSX as side-effects instead of returning |
| **Widget properties** | ViewModel | Strongly-typed data passed from parent to child |
| **State** | State | Internal component state with TypeScript typing |
| **BuildContext** | Context | Component context and dependencies |
| **Key** | key | Unique identifier for components |

## Your First Valdi Component

Let's start by building a simple counter app in Valdi. If you've built similar components in Flutter, you'll notice many familiar patterns:

### Building a Counter in Valdi
```tsx
import { StatefulComponent } from 'valdi_core/src/Component';
import { systemBoldFont, systemFont } from 'valdi_core/src/SystemFont';

interface CounterViewModel {
  title: string;
}

interface CounterState {
  counter: number;
}

export class CounterComponent extends StatefulComponent<CounterViewModel, CounterState> {
  state = {
    counter: 0,
  };

  private incrementCounter = () => {
    this.setState({
      counter: this.state.counter + 1,
    });
  };

  onRender(): void {
    <layout
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      padding={20}
    >
      <label
        value={this.viewModel.title}
        font={systemBoldFont(24)}
        color="black"
        marginBottom={10}
      />
      <label
        value={`Count: ${this.state.counter}`}
        font={systemFont(18)}
        color="black"
        marginBottom={20}
      />
      <view
        backgroundColor="blue"
        padding={12}
        borderRadius={8}
        onTap={this.incrementCounter}
      >
        <label
          value="Increment"
          color="white"
          font={systemFont(16)}
        />
      </view>
    </layout>;
  }
}
```

### Key Differences from Flutter

If you're coming from Flutter, here are the main differences to notice:

**TypeScript Interfaces**: The `CounterViewModel` and `CounterState` interfaces provide strong typing that helps catch errors at compile time and gives you excellent IDE support.

**Side-Effect Rendering**: Instead of returning JSX from `onRender()`, you emit it as statements. The Valdi compiler transforms JSX like `<layout></layout>;` into renderer calls that build the UI tree as side-effects.

**CSS Flexbox Layout**: The `<layout>` element uses familiar CSS Flexbox properties like `flexDirection`, `justifyContent`, and `alignItems`.

**Native Elements**: `<layout>`, `<label>`, and `<view>` are Valdi's native elements that render directly to platform views.

**System Fonts**: `systemBoldFont()` and `systemFont()` automatically use the appropriate fonts for each platform.

### How This Looks in Flutter

For comparison, here's the same component in Flutter:

```dart
import 'package:flutter/material.dart';

class CounterWidget extends StatefulWidget {
  final String title;

  CounterWidget({required this.title});

  @override
  _CounterWidgetState createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          widget.title,
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(
          'Count: $_counter',
          style: TextStyle(fontSize: 18),
        ),
        ElevatedButton(
          onPressed: _incrementCounter,
          child: Text('Increment'),
        ),
      ],
    );
  }
}
```

## Understanding Key Differences

As you transition from Flutter to Valdi, here are the main architectural differences to understand:

### 1. Side-Effect Rendering vs Return-Based Rendering

**In Valdi**: The `onRender()` method emits JSX as side-effects and returns `void`
```tsx
onRender(): void {
  <layout>  // JSX gets compiled to renderer.beginRender() calls
    <label value="Hello" />
  </layout>;  // Semicolon makes this a statement that triggers rendering
}
```

**In Flutter**: The `build()` method returns a Widget tree
```dart
@override
Widget build(BuildContext context) {
  return Column(children: [...]);  // Returns the widget
}
```

**How Valdi's Side-Effect Rendering Works**: When you write JSX in `onRender()`, the Valdi compiler transforms it into renderer calls like `__Renderer.beginRender()` and `__Renderer.endRender()`. These calls accumulate to build the UI tree as side-effects of executing the JSX statements, rather than returning a tree structure.

### 2. ViewModels vs Constructor Properties

**In Valdi**: Properties are passed via strongly-typed ViewModel interfaces
```tsx
interface MyViewModel {
  title: string;
  count: number;
}

// Usage: <MyComponent title="Hello" count={5} />
// Access: this.viewModel.title, this.viewModel.count
```

**In Flutter**: Properties are passed directly to the widget constructor
```dart
MyWidget(title: "Hello", count: 5)
// Access: widget.title, widget.count
```

The ViewModel approach gives you strong TypeScript typing and better IDE support.

### 3. CSS Flexbox vs Flutter Layout

**In Valdi**: Uses CSS Flexbox layout system
```tsx
<layout
  flexDirection="column"
  justifyContent="center"
  alignItems="center"
>
  {/* children */}
</layout>
```

**In Flutter**: Uses its own layout system with widgets
```dart
Column(
  mainAxisAlignment: MainAxisAlignment.center,
  children: [...]
)
```

If you have web development experience, Valdi's Flexbox will feel familiar.

## Component Lifecycle

Valdi provides a clean, predictable component lifecycle that will feel familiar to Flutter developers:

| Valdi Lifecycle | Flutter Equivalent | Purpose |
|-----------------|-------------------|---------|
| **onCreate()** | initState() | Component initialization |
| **onRender()** | build() | UI rendering |
| **onDestroy()** | dispose() | Cleanup when component is removed |
| **onViewModelUpdate()** | didUpdateWidget() | When parent updates properties |

### Valdi Lifecycle Example
```tsx
export class MyComponent extends StatefulComponent<ViewModel, State> {
  onCreate(): void {
    // Initialize component
  }

  onRender(): void {
    <view>
      <label value="Hello" />
    </view>;
  }

  onDestroy(): void {
    // Cleanup
  }

  onViewModelUpdate(): void {
    // Called when parent updates viewModel
  }
}
```

### Flutter Lifecycle Example (for comparison)
```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  @override
  void initState() {
    super.initState();
    // Initialize component
  }

  @override
  Widget build(BuildContext context) {
    return Container(child: Text('Hello'));
  }

  @override
  void dispose() {
    // Cleanup
    super.dispose();
  }
}
```

## Valdi's Native Elements

Valdi provides a powerful set of native elements that render directly to platform-specific views, delivering authentic native performance and behavior:

| Valdi Element | Flutter Equivalent | Description |
|---------------|-------------------|-------------|
| **`<view>`** | Container | Basic rectangular container with styling - renders to native views |
| **`<layout>`** | Column/Row | Invisible layout container using flexbox - optimized for layout only |
| **`<label>`** | Text | Text display element - uses native text rendering |
| **`<image>`** | Image | Image display element - leverages native image optimization |
| **`<scroll>`** | SingleChildScrollView | Scrollable container - native scrolling performance |
| **`<textfield>`** | TextField | Single-line text input - native input handling |
| **`<layout>`** | *(no equivalent)* | Lightweight layout-only container - unique to Valdi |

### Valdi Native Elements Example
```tsx
<view
  padding={16}
  backgroundColor="blue"
  borderRadius={8}
>
  <layout flexDirection="column">
    <label value="Hello World" />
    <image src="https://example.com/image.png" />
  </layout>
</view>
```

**Key Advantages:**
- **Native Performance**: Each element renders to actual platform views (UIView/View)
- **Platform Authenticity**: Automatic platform-specific behaviors and styling
- **Optimized Layout**: `<layout>` is lightweight and optimized for layout-only scenarios
- **Flexbox Power**: Familiar CSS Flexbox properties for intuitive layout

### Flutter Widget Example (for comparison)
```dart
Container(
  padding: EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(8),
  ),
  child: Column(
    children: [
      Text('Hello World'),
      Image.network('https://example.com/image.png'),
    ],
  ),
)
```

## State Management Patterns

Valdi provides intuitive state management with TypeScript's type safety and familiar reactive patterns:

### Valdi State Management
```tsx
interface TodoState {
  todos: string[];
}

export class TodoList extends StatefulComponent<{}, TodoState> {
  state = {
    todos: [] as string[],
  };

  private addTodo = (todo: string) => {
    this.setState({
      todos: [...this.state.todos, todo],
    });
  };

  private removeTodo = (index: number) => {
    const newTodos = [...this.state.todos];
    newTodos.splice(index, 1);
    this.setState({
      todos: newTodos,
    });
  };

  onRender(): void {
    <layout flexDirection="column">
      {this.renderTodos()}
    </layout>;
  }

  private renderTodos(): void {
    for (let i = 0; i < this.state.todos.length; i++) {
      const todo = this.state.todos[i];
      <view
        flexDirection="row"
        justifyContent="space-between"
        padding={8}
        key={i}
      >
        <label value={todo} />
        <view
          backgroundColor="red"
          padding={4}
          onTap={() => this.removeTodo(i)}
        >
          <label value="Delete" color="white" />
        </view>
      </view>;
    }
  }
}
```

**Valdi State Management Benefits:**
- **Type Safety**: Strong TypeScript typing for state interfaces
- **Immutable Updates**: Encourages immutable state patterns
- **Performance**: Efficient re-rendering only when state actually changes
- **Debugging**: Excellent debugging support with TypeScript tooling

### Flutter State Management (for comparison)
```dart
class TodoList extends StatefulWidget {
  @override
  _TodoListState createState() => _TodoListState();
}

class _TodoListState extends State<TodoList> {
  List<String> todos = [];

  void addTodo(String todo) {
    setState(() {
      todos.add(todo);
    });
  }

  void removeTodo(int index) {
    setState(() {
      todos.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: todos.map((todo) =>
        ListTile(
          title: Text(todo),
          trailing: IconButton(
            icon: Icon(Icons.delete),
            onPressed: () => removeTodo(todos.indexOf(todo)),
          ),
        )
      ).toList(),
    );
  }
}
```

## Styling System

Valdi offers a powerful, flexible styling system that combines the best of CSS-like properties with TypeScript's type safety:

### Valdi Styling
```tsx
import { Style } from 'valdi_core/src/Style';
import { View } from 'valdi_tsx/src/NativeTemplateElements';

const styles = {
  container: new Style<View>({
    width: 200,
    height: 100,
    padding: 16,
    marginTop: 8,
    marginBottom: 8,
    backgroundColor: 'blue',
    borderRadius: 12,
    boxShadow: '0 2 4 rgba(0, 0, 0, 0.26)',
  }),
};

// Usage in component:
<view style={styles.container}>
  <label
    value="Styled Container"
    color="white"
    font={systemBoldFont(18)}
  />
</view>
```

**Valdi Styling Advantages:**
- **Type Safety**: Style objects are strongly typed for each element type
- **CSS-like Properties**: Familiar CSS properties that map to native styling
- **Performance**: Styles are compiled and optimized for native rendering
- **Reusability**: Style objects can be shared across components
- **IntelliSense**: Full IDE support with autocomplete and validation

### Flutter Styling (for comparison)
```dart
Container(
  width: 200,
  height: 100,
  padding: EdgeInsets.all(16),
  margin: EdgeInsets.symmetric(vertical: 8),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: Colors.black26,
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
    ],
  ),
  child: Text(
    'Styled Container',
    style: TextStyle(
      color: Colors.white,
      fontSize: 18,
      fontWeight: FontWeight.bold,
    ),
  ),
)
```

## List Rendering Patterns

Valdi provides efficient, intuitive list rendering with native scrolling performance:

### Valdi Lists
```tsx
// In onRender method:
<scroll>
  {this.renderItems()}
</scroll>

private renderItems(): void {
  for (const item of this.state.items) {
    <view key={item.id} padding={8}>
      <label value={item.title} font={systemBoldFont(16)} />
      <label value={item.description} font={systemFont(14)} />
    </view>;
  }
}
```

**Valdi List Benefits:**
- **Native Scrolling**: Uses platform-native scrolling for optimal performance
- **Flexible Rendering**: Render any complex layout within list items
- **Type Safety**: Full TypeScript support for item data
- **Memory Efficient**: Automatic view recycling and optimization

### Flutter Lists (for comparison)
```dart
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
      subtitle: Text(items[index].description),
    );
  },
)
```

## Event Handling

Valdi provides intuitive, native event handling with excellent TypeScript support:

### Valdi Event Handling
```tsx
<view
  onTap={() => {
    console.log('Tapped!');
  }}
  onLongPress={() => {
    console.log('Long pressed!');
  }}
>
  <label value="Tap me" />
</view>
```

**Valdi Event Advantages:**
- **Native Events**: Direct mapping to platform-native gesture recognizers
- **Type Safety**: Event handlers are strongly typed
- **Performance**: No bridge overhead for event handling
- **Familiar API**: Similar to web event handling patterns

### Flutter Event Handling (for comparison)
```dart
GestureDetector(
  onTap: () {
    print('Tapped!');
  },
  onLongPress: () {
    print('Long pressed!');
  },
  child: Container(
    child: Text('Tap me'),
  ),
)
```

## Navigation Patterns

Valdi provides a powerful, type-safe navigation system with native performance:

### Valdi Navigation
```tsx
import { NavigationPageComponent } from 'valdi_navigation/src/NavigationPageComponent';
import { NavigationPage } from 'valdi_navigation/src/NavigationPage';

// Note: 'module' refers to the current module object containing path and exports
@NavigationPage(module)
export class SecondPage extends NavigationPageComponent<{}> {
  onRender(): void {
    <view>
      <label value="Second Page" />
      <view
        backgroundColor="gray"
        padding={8}
        onTap={() => this.navigationController.dismiss(true)}
      >
        <label value="Go Back" />
      </view>
    </view>;
  }
}

// In your main component:
export class MainPage extends NavigationPageComponent<{}> {
  private navigateToSecond = () => {
    this.navigationController.push(SecondPage, {}, {}, { animated: true });
  };

  onRender(): void {
    <view>
      <view
        backgroundColor="blue"
        padding={8}
        onTap={this.navigateToSecond}
      >
        <label value="Go to Second Page" color="white" />
      </view>
    </view>;
  }
}
```

**Valdi Navigation Benefits:**
- **Type Safety**: Strongly typed navigation parameters and return values
- **Native Performance**: Uses platform-native navigation controllers
- **Decorator Pattern**: Clean, declarative page registration
- **Flexible**: Support for modals, push/pop, and custom transitions

### Flutter Navigation (for comparison)
```dart
// Navigate to new screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => SecondScreen()),
);

// Navigate back
Navigator.pop(context);

// Named routes
Navigator.pushNamed(context, '/second');
```

## Component Composition

Valdi provides powerful component composition with TypeScript's type safety and flexible children patterns:

### Valdi Composition
```tsx
interface CustomCardViewModel {
  title: string;
  children?: () => void;  // Render function for children
}

export class CustomCard extends Component<CustomCardViewModel> {
  onRender(): void {
    <view
      backgroundColor="white"
      borderRadius={8}
      padding={16}
      boxShadow="0 2 4 rgba(0, 0, 0, 0.1)"
    >
      <label
        value={this.viewModel.title}
        font={systemBoldFont(16)}
        marginBottom={8}
      />
      {this.viewModel.children?.()}
    </view>;
  }
}

// Usage:
<CustomCard title="My Card">
  {() => {
    <label value="Card content" />;
  }}
</CustomCard>
```

**Valdi Composition Benefits:**
- **Type Safety**: Strongly typed ViewModels and children functions
- **Flexible Children**: Support for render functions and complex child patterns
- **Performance**: Efficient re-rendering with proper component boundaries
- **Reusability**: Easy to create and share reusable components

### Flutter Composition (for comparison)
```dart
class CustomCard extends StatelessWidget {
  final Widget child;
  final String title;

  CustomCard({required this.child, required this.title});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          Text(title, style: TextStyle(fontWeight: FontWeight.bold)),
          child,
        ],
      ),
    );
  }
}

// Usage:
CustomCard(
  title: "My Card",
  child: Text("Card content"),
)
```

## Performance Considerations

Valdi is designed for optimal performance with native rendering and smart optimizations:

### Valdi Performance Tips
- **Use `<layout>` for layout-only containers**: More efficient than `<view>` when you don't need native view features
- **Create `Style` objects at initialization**: Don't create styles during render for better performance
- **Use `key` props for list items**: Maintains component identity and enables efficient updates
- **Avoid creating functions during render**: Use class methods or `createReusableCallback` for optimal performance

```tsx
// ❌ Bad - creates new function on every render
<view onTap={() => this.handleTap()}>

// ✅ Good - reuses the same function reference
<view onTap={this.handleTap}>

// ✅ Good - for functions with parameters, use createReusableCallback
import { createReusableCallback } from 'valdi_core/src/utils/Callback';

private handleItemTap = createReusableCallback((index: number) => {
  // Handle tap with index
});
```

**Why Valdi Performs Better:**
- **Native Rendering**: Direct rendering to platform views eliminates custom graphics overhead
- **Optimized Layout**: `<layout>` elements are lightweight and optimized for layout-only scenarios
- **Smart Re-rendering**: Only updates components when state or viewModel actually changes
- **No Bridge Overhead**: Direct native integration without JavaScript bridge delays

### Flutter Performance Tips (for comparison)
- Use `const` constructors when possible
- Implement `shouldRebuild` for complex widgets
- Use `ListView.builder` for large lists
- Avoid rebuilding expensive widgets

## Development Workflow

Valdi provides an excellent development experience with powerful tooling and fast iteration cycles:

### Setting Up Your Development Environment

1. **Prerequisites**: Node.js, TypeScript, and platform-specific tools (Xcode for iOS, Android Studio for Android)

2. **Quick Project Setup**:
```bash
# Clone Valdi repository
<NAME_EMAIL>:Snapchat/Valdi.git

# Install CLI tools
cd Valdi/npm_modules/cli/
npm run cli:install

# Set up development environment
valdi dev_setup

# Create new project
mkdir my_valdi_app
cd my_valdi_app
valdi bootstrap
```

3. **Running Your App**:
```bash
# Sync project for VSCode (enables syntax highlighting and autocomplete)
valdi projectsync

# Install for iOS
valdi install ios

# Install for Android
valdi install android

# Start hot reload
valdi hotreload
```

### Valdi's Superior Development Experience

**🔥 Hot Reload**: State-preserving hot reload on all platforms, including on-device testing
**🛠️ TypeScript Tooling**: Full IDE support with autocomplete, type checking, and refactoring
**🐛 Advanced Debugging**: Use familiar web dev tools, VSCode debugger, and console.log statements
**⚡ Fast Builds**: Efficient compilation and deployment cycles

### Hot Reload Comparison

**Valdi**: Advanced hot reload with state preservation and instant UI updates across all platforms
**Flutter**: Similar hot reload experience but limited to Flutter's custom rendering engine

### Debugging Capabilities

**Valdi**:
- Browser dev tools integration
- VSCode debugger support
- TypeScript stack traces
- Console.log statements with full context

```tsx
// Debugging in Valdi
export class DebugComponent extends Component {
  onRender(): void {
    console.log('Rendering with viewModel:', this.viewModel);

    <view>
      <label value="Debug Component" />
    </view>;
  }
}
```

**Flutter Debugging** (for comparison):
- Flutter Inspector and widget tree
- Dart debugger and print statements
- Custom rendering engine debugging tools

## Transitioning from Flutter to Valdi

### Migration Strategy

1. **Leverage Your Flutter Knowledge**:
   - Component-based thinking translates directly
   - State management patterns are similar
   - Reactive programming concepts apply

2. **Embrace TypeScript Benefits**:
   - Strong typing prevents runtime errors
   - Excellent IDE support and refactoring
   - Access to vast JavaScript ecosystem

3. **Adopt Native-First Mindset**:
   - Think in terms of native platform views
   - Leverage platform-specific behaviors
   - Optimize for authentic user experiences

### Converting Flutter Widgets to Valdi Components

1. **Class Structure**:
   - `StatelessWidget` → `Component<ViewModel>`
   - `StatefulWidget` → `StatefulComponent<ViewModel, State>`

2. **Render Method**:
   - `build()` → `onRender()`
   - Return statement → Side-effect JSX

3. **Properties**:
   - Constructor parameters → ViewModel interface
   - `widget.property` → `this.viewModel.property`

4. **Styling**:
   - Flutter styling → Valdi Style objects or inline attributes
   - Flutter layout widgets → CSS Flexbox layout

5. **Event Handlers**:
   - Flutter callbacks → Valdi event handlers

## Best Practices for Flutter Developers

1. **Embrace Component Architecture**: Build small, focused, reusable components just like Flutter widgets

2. **Leverage TypeScript's Power**: Use strong typing, interfaces, and modern JavaScript features for better code quality

3. **Think Native-First**: Take advantage of Valdi's native rendering for authentic platform experiences

4. **Start Simple with State**: Begin with component state, then scale to external state management as needed

5. **Test Thoroughly**: Write comprehensive unit tests for business logic and integration tests for components

## Next Steps

Ready to start building with Valdi? Here's your roadmap:

1. **🚀 Get Started**: [Getting Started Codelab](./start-code-lab.md) - Build your first Valdi app
2. **📚 Deep Dive into Core Concepts**:
   - [Component Lifecycle](./core-component.md) - Master component patterns
   - [State Management](./core-states.md) - Handle complex application state
   - [Styling System](./core-styling.md) - Create beautiful, native-looking UIs
   - [Layout with Flexbox](./core-flexbox.md) - Build responsive layouts
3. **🛠️ Build Real Apps**: Start with a todo app or counter, then scale up
4. **🤝 Join the Community**: Connect with other developers on [Discord](https://discord.gg/sqMERrCVYF)

## Making the Transition

As a Flutter developer, you have a strong foundation for learning Valdi. Here's how to approach the transition:

### What Transfers Directly
- **Component thinking**: Your understanding of breaking UI into reusable components
- **State management patterns**: Similar concepts for managing and updating component state
- **Reactive programming**: UI updates automatically when state changes
- **Development workflow**: Hot reload, debugging, and iterative development

### What You'll Need to Learn
- **TypeScript/JavaScript**: If you're not familiar with these languages
- **CSS Flexbox**: For layout (though it's quite intuitive if you know CSS)
- **Side-effect rendering**: The pattern of emitting JSX as statements
- **Native elements**: Valdi's set of native UI elements

### Learning Path Recommendations

1. **Start with TypeScript basics** if you're not familiar with it
2. **Build simple components** to get comfortable with the syntax
3. **Practice with layouts** using CSS Flexbox properties
4. **Explore state management** patterns in Valdi
5. **Try navigation** and more complex app structures

## Conclusion

Your Flutter experience gives you a significant head start with Valdi. The core concepts of component-based architecture, reactive programming, and declarative UI design translate directly. The main differences are in the implementation details - TypeScript instead of Dart, CSS Flexbox instead of Flutter's layout system, and native rendering instead of custom graphics.

**What you already know:**
- Component-based thinking and architecture
- State management and reactive patterns
- Cross-platform development concepts
- Hot reload development workflow

**What you'll learn:**
- TypeScript's type system and modern JavaScript features
- CSS Flexbox for intuitive layout
- Native rendering and platform integration
- Valdi's specific APIs and patterns

The transition should feel natural, building on your existing knowledge while introducing you to new tools and approaches. Take your time to understand the differences, and you'll find that many of the skills you've developed with Flutter apply directly to Valdi development.

Happy coding with Valdi! 🚀